package net.jqsoft.szzq.basic.framework.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件类型工具类
 * 
 * <AUTHOR>
 * @since 2025-07-27
 */
public class FileTypeUtil {

    /**
     * 文件扩展名到MIME类型的映射
     */
    private static final Map<String, String> EXTENSION_TO_MIME_TYPE = new HashMap<>();
    
    /**
     * 文件扩展名到文件类型的映射
     */
    private static final Map<String, String> EXTENSION_TO_FILE_TYPE = new HashMap<>();

    static {
        // 图片类型
        EXTENSION_TO_MIME_TYPE.put("jpg", "image/jpeg");
        EXTENSION_TO_MIME_TYPE.put("jpeg", "image/jpeg");
        EXTENSION_TO_MIME_TYPE.put("png", "image/png");
        EXTENSION_TO_MIME_TYPE.put("gif", "image/gif");
        EXTENSION_TO_MIME_TYPE.put("bmp", "image/bmp");
        EXTENSION_TO_MIME_TYPE.put("webp", "image/webp");
        EXTENSION_TO_MIME_TYPE.put("svg", "image/svg+xml");
        EXTENSION_TO_MIME_TYPE.put("ico", "image/x-icon");

        // 文档类型
        EXTENSION_TO_MIME_TYPE.put("pdf", "application/pdf");
        EXTENSION_TO_MIME_TYPE.put("doc", "application/msword");
        EXTENSION_TO_MIME_TYPE.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        EXTENSION_TO_MIME_TYPE.put("xls", "application/vnd.ms-excel");
        EXTENSION_TO_MIME_TYPE.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        EXTENSION_TO_MIME_TYPE.put("ppt", "application/vnd.ms-powerpoint");
        EXTENSION_TO_MIME_TYPE.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");

        // 文本类型
        EXTENSION_TO_MIME_TYPE.put("txt", "text/plain");
        EXTENSION_TO_MIME_TYPE.put("rtf", "application/rtf");
        EXTENSION_TO_MIME_TYPE.put("csv", "text/csv");

        // 压缩文件
        EXTENSION_TO_MIME_TYPE.put("zip", "application/zip");
        EXTENSION_TO_MIME_TYPE.put("rar", "application/x-rar-compressed");
        EXTENSION_TO_MIME_TYPE.put("7z", "application/x-7z-compressed");
        EXTENSION_TO_MIME_TYPE.put("tar", "application/x-tar");
        EXTENSION_TO_MIME_TYPE.put("gz", "application/gzip");

        // 音频类型
        EXTENSION_TO_MIME_TYPE.put("mp3", "audio/mpeg");
        EXTENSION_TO_MIME_TYPE.put("wav", "audio/wav");
        EXTENSION_TO_MIME_TYPE.put("flac", "audio/flac");
        EXTENSION_TO_MIME_TYPE.put("aac", "audio/aac");

        // 视频类型
        EXTENSION_TO_MIME_TYPE.put("mp4", "video/mp4");
        EXTENSION_TO_MIME_TYPE.put("avi", "video/x-msvideo");
        EXTENSION_TO_MIME_TYPE.put("mov", "video/quicktime");
        EXTENSION_TO_MIME_TYPE.put("wmv", "video/x-ms-wmv");
        EXTENSION_TO_MIME_TYPE.put("flv", "video/x-flv");

        // 文件类型分类
        EXTENSION_TO_FILE_TYPE.put("jpg", "图片");
        EXTENSION_TO_FILE_TYPE.put("jpeg", "图片");
        EXTENSION_TO_FILE_TYPE.put("png", "图片");
        EXTENSION_TO_FILE_TYPE.put("gif", "图片");
        EXTENSION_TO_FILE_TYPE.put("bmp", "图片");
        EXTENSION_TO_FILE_TYPE.put("webp", "图片");
        EXTENSION_TO_FILE_TYPE.put("svg", "图片");
        EXTENSION_TO_FILE_TYPE.put("ico", "图片");

        EXTENSION_TO_FILE_TYPE.put("pdf", "文档");
        EXTENSION_TO_FILE_TYPE.put("doc", "文档");
        EXTENSION_TO_FILE_TYPE.put("docx", "文档");
        EXTENSION_TO_FILE_TYPE.put("xls", "文档");
        EXTENSION_TO_FILE_TYPE.put("xlsx", "文档");
        EXTENSION_TO_FILE_TYPE.put("ppt", "文档");
        EXTENSION_TO_FILE_TYPE.put("pptx", "文档");
        EXTENSION_TO_FILE_TYPE.put("txt", "文档");
        EXTENSION_TO_FILE_TYPE.put("rtf", "文档");
        EXTENSION_TO_FILE_TYPE.put("csv", "文档");

        EXTENSION_TO_FILE_TYPE.put("zip", "压缩文件");
        EXTENSION_TO_FILE_TYPE.put("rar", "压缩文件");
        EXTENSION_TO_FILE_TYPE.put("7z", "压缩文件");
        EXTENSION_TO_FILE_TYPE.put("tar", "压缩文件");
        EXTENSION_TO_FILE_TYPE.put("gz", "压缩文件");

        EXTENSION_TO_FILE_TYPE.put("mp3", "音频");
        EXTENSION_TO_FILE_TYPE.put("wav", "音频");
        EXTENSION_TO_FILE_TYPE.put("flac", "音频");
        EXTENSION_TO_FILE_TYPE.put("aac", "音频");

        EXTENSION_TO_FILE_TYPE.put("mp4", "视频");
        EXTENSION_TO_FILE_TYPE.put("avi", "视频");
        EXTENSION_TO_FILE_TYPE.put("mov", "视频");
        EXTENSION_TO_FILE_TYPE.put("wmv", "视频");
        EXTENSION_TO_FILE_TYPE.put("flv", "视频");
    }

    /**
     * 根据文件扩展名获取MIME类型
     * 
     * @param extension 文件扩展名（不包含点号）
     * @return MIME类型，如果未知则返回"application/octet-stream"
     */
    public static String getMimeType(String extension) {
        if (extension == null || extension.isEmpty()) {
            return "application/octet-stream";
        }
        
        String mimeType = EXTENSION_TO_MIME_TYPE.get(extension.toLowerCase());
        return mimeType != null ? mimeType : "application/octet-stream";
    }

    /**
     * 根据文件扩展名获取文件类型
     * 
     * @param extension 文件扩展名（不包含点号）
     * @return 文件类型，如果未知则返回"其他"
     */
    public static String getFileType(String extension) {
        if (extension == null || extension.isEmpty()) {
            return "其他";
        }
        
        String fileType = EXTENSION_TO_FILE_TYPE.get(extension.toLowerCase());
        return fileType != null ? fileType : "其他";
    }

    /**
     * 从文件名中提取扩展名
     * 
     * @param fileName 文件名
     * @return 文件扩展名（不包含点号），如果没有扩展名则返回空字符串
     */
    public static String getExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return "";
    }

    /**
     * 检查文件类型是否为图片
     * 
     * @param extension 文件扩展名
     * @return 是否为图片
     */
    public static boolean isImage(String extension) {
        return "图片".equals(getFileType(extension));
    }

    /**
     * 检查文件类型是否为文档
     * 
     * @param extension 文件扩展名
     * @return 是否为文档
     */
    public static boolean isDocument(String extension) {
        return "文档".equals(getFileType(extension));
    }

    /**
     * 检查文件类型是否为压缩文件
     * 
     * @param extension 文件扩展名
     * @return 是否为压缩文件
     */
    public static boolean isArchive(String extension) {
        return "压缩文件".equals(getFileType(extension));
    }

    /**
     * 检查文件类型是否为音频
     * 
     * @param extension 文件扩展名
     * @return 是否为音频
     */
    public static boolean isAudio(String extension) {
        return "音频".equals(getFileType(extension));
    }

    /**
     * 检查文件类型是否为视频
     * 
     * @param extension 文件扩展名
     * @return 是否为视频
     */
    public static boolean isVideo(String extension) {
        return "视频".equals(getFileType(extension));
    }

    /**
     * 检查文件扩展名是否被支持
     *
     * @param extension 文件扩展名
     * @return 是否被支持
     */
    public static boolean isSupported(String extension) {
        return EXTENSION_TO_MIME_TYPE.containsKey(extension.toLowerCase());
    }

    /**
     * 根据文件路径获取文件类型
     *
     * @param filePath 文件路径
     * @return 文件类型
     */
    public static String getFileCategory(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "其他";
        }

        String extension = getExtension(filePath);
        return getFileType(extension);
    }
}
