#----------------------     工具参数        -----------------------
spring:
  datasource: #数据源配置（这里是开发库的地址）
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: ***********************************************************************************
          driver-class-name: oracle.jdbc.OracleDriver
          username: dbframework_gz
          password: dbframework_gz369
          # 连接池配置 - 减少并发以保护OSS服务
          hikari:
            maximum-pool-size: 20        # 最大连接数20（从50降低）
            minimum-idle: 8              # 最小空闲连接8（从20降低）
            connection-timeout: 30000    # 连接超时30秒
            idle-timeout: 600000         # 空闲超时10分钟
            max-lifetime: 1800000        # 最大生命周期30分钟
            leak-detection-threshold: 60000 # 泄漏检测1分钟
        slave_1:
          url: jdbc:dm://172.18.5.157:5236/ZHMZ_INTEGRATERELIEF?zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
          driver-class-name: dm.jdbc.driver.DmDriver
          username: sysdba
          password: DmJQkj_5350@
          # 连接池配置 - 减少并发以保护OSS服务
          hikari:
            maximum-pool-size: 20        # 最大连接数20（从50降低）
            minimum-idle: 8              # 最小空闲连接8（从20降低）
            connection-timeout: 30000    # 连接超时30秒
            idle-timeout: 600000         # 空闲超时10分钟
            max-lifetime: 1800000        # 最大生命周期30分钟
            leak-detection-threshold: 60000 # 泄漏检测1分钟
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
#OSS上传接口地址
oss:
  upload:
    url: http://************:9080/oss/v1/upload
file:
  url:
    prefix: http://*************:8091/dbgl
swagger2:
  # 是否开启swagger2 开启为true，关闭为false
  enable: true

migration:
  bulk:
    # 基础配置 - 减少并发以保护OSS服务
    chunk-size: 5000                    # 每次查询2000条（从5000降低）
    parallel-threads: 10                 # 并行线程10个（从20降低）
    batch-insert-size: 1000              # 批量插入500条（从1000降低）

    # 超时配置 - 适应网络环境
    connection-timeout: 30000           # 连接超时30秒
    read-timeout: 120000                # 读取超时120秒（增加到2分钟）
    retry-interval: 1000                # 重试间隔1秒（减少等待）

    # 重试配置
    max-retry-count: 3                  # 最大重试次数
    enable-retry: true                  # 启用失败重试

    # 功能开关配置
    enable-resume: true                 # 启用断点续传
    enable-monitoring: true             # 启用性能监控

    # 性能优化配置
    gc-trigger-threshold: 50000         # 5万条触发GC（原1万）
    progress-report-interval: 5000      # 每5000条报告进度（原1000）

    # 持续监控配置
    enable-continuous-monitoring: true   # 启用持续监控
    continuous-monitoring-interval: 10000 # 10秒检查间隔（原30秒）
    skip-failed-records: true          # 跳过失败记录

    # 高性能模式配置 - 减少并发以保护OSS服务
    db-pool-size: 25                    # 数据库连接池20个（从50降低）
    oss-upload-concurrency: 50          # OSS上传并发10个（从100大幅降低）
    memory-queue-size: 5000             # 内存队列5000条（从10000降低）
    prefetch-batches: 2                 # 预取2个批次（从3降低）
    enable-batch-prefetch: true         # 启用批量预取

    # 性能监控配置
    performance-monitor-interval: 30     # 30秒监控间隔
    target-processing-speed: 200        # 目标200条/秒

    # 动态线程调整 - 减少线程数量
    enable-dynamic-thread-adjustment: true
    max-threads: 20                     # 最大20个线程（从50降低）
    min-threads: 8                      # 最小5个线程（从10降低）

    # 文件处理配置
    max-file-size: 209715200           # 最大文件200MB（原100MB）
    download-buffer-size: 65536         # 下载缓冲64KB（原8KB）
    delete-temp-files: true             # 删除临时文件
    supported-file-types: "jpg,jpeg,png,gif,bmp,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar"  # 支持的文件类型
    temp-directory: D:\temp   # 临时文件目录

    # 数据库配置
    db-max-wait-time: 5000              # 数据库最大等待5秒
    batch-queue-capacity: 2000          # 批处理队列2000（从5000降低）
# 日志配置
logging:
  level:
    net.jqsoft.szzq.basic.framework: INFO
    org.springframework.jdbc: WARN
    com.zaxxer.hikari: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/migration.log
  logback:
    rolling policy:
      max-file-size: 100MB
      max-history: 30
# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
# 线程池配置
thread:
  pool:
    # 文件迁移线程池 - 减少线程数量
    file-migration:
      core-pool-size: 10                # 核心线程数（从20降低）
      maximum-pool-size: 20            # 最大线程数（从50降低）
      keep-alive-time: 60              # 空闲时间（秒）
      queue-capacity: 500              # 队列容量（从1000降低）
      thread-name-prefix: "FileMigration-"