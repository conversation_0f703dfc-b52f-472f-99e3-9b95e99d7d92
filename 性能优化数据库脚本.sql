-- 性能优化数据库脚本 - 提升迁移效率
-- 请在Oracle数据库中执行

-- 1. 创建高性能索引
BEGIN
    -- DB_BZCLB表索引
    EXECUTE IMMEDIATE 'CREATE INDEX IDX_DB_BZCLB_COMPOSITE ON DB_BZCLB(SPATH, IDELETEMARK, DCREATEDATE)';
    DBMS_OUTPUT.PUT_LINE('复合索引 IDX_DB_BZCLB_COMPOSITE 创建成功');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -955 THEN
            DBMS_OUTPUT.PUT_LINE('复合索引 IDX_DB_BZCLB_COMPOSITE 已存在');
        ELSE
            DBMS_OUTPUT.PUT_LINE('创建复合索引失败: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    -- D1_FAMILYINFO表索引
    EXECUTE IMMEDIATE 'CREATE INDEX IDX_D1_FAMILYINFO_DELETE ON D1_FAMILYINFO(IDELETEMARK, GID)';
    DBMS_OUTPUT.PUT_LINE('索引 IDX_D1_FAMILYINFO_DELETE 创建成功');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -955 THEN
            DBMS_OUTPUT.PUT_LINE('索引 IDX_D1_FAMILYINFO_DELETE 已存在');
        ELSE
            DBMS_OUTPUT.PUT_LINE('创建索引失败: ' || SQLERRM);
        END IF;
END;
/

BEGIN
    -- ATTACHMENT_MIGRATION_RECORD表高性能索引
    EXECUTE IMMEDIATE 'CREATE INDEX IDX_AMR_STATUS_RETRY_GOBJECTID ON ATTACHMENT_MIGRATION_RECORD(STATUS, RETRY_COUNT, GOBJECTID)';
    DBMS_OUTPUT.PUT_LINE('复合索引 IDX_AMR_STATUS_RETRY_GOBJECTID 创建成功');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -955 THEN
            DBMS_OUTPUT.PUT_LINE('复合索引 IDX_AMR_STATUS_RETRY_GOBJECTID 已存在');
        ELSE
            DBMS_OUTPUT.PUT_LINE('创建复合索引失败: ' || SQLERRM);
        END IF;
END;
/

-- 2. 优化表统计信息
BEGIN
    -- 收集所有相关表的统计信息
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'DB_BZCLB',
        estimate_percent => 10,
        method_opt => 'FOR ALL COLUMNS SIZE AUTO',
        cascade => TRUE
    );
    DBMS_OUTPUT.PUT_LINE('DB_BZCLB 统计信息收集完成');
END;
/

BEGIN
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'D1_FAMILYINFO',
        estimate_percent => 10,
        method_opt => 'FOR ALL COLUMNS SIZE AUTO',
        cascade => TRUE
    );
    DBMS_OUTPUT.PUT_LINE('D1_FAMILYINFO 统计信息收集完成');
END;
/

BEGIN
    DBMS_STATS.GATHER_TABLE_STATS(
        ownname => USER,
        tabname => 'ATTACHMENT_MIGRATION_RECORD',
        estimate_percent => 100,
        method_opt => 'FOR ALL COLUMNS SIZE AUTO',
        cascade => TRUE
    );
    DBMS_OUTPUT.PUT_LINE('ATTACHMENT_MIGRATION_RECORD 统计信息收集完成');
END;
/

-- 3. 创建物化视图（可选，用于超大数据量场景）
BEGIN
    EXECUTE IMMEDIATE '
    CREATE MATERIALIZED VIEW MV_UNMIGRATED_FILES_SUMMARY
    BUILD IMMEDIATE
    REFRESH FAST ON DEMAND
    AS
    SELECT 
        COUNT(*) as TOTAL_FILES,
        COUNT(CASE WHEN db.SPATH IS NOT NULL THEN 1 END) as FILES_WITH_PATH,
        MIN(db.DCREATEDATE) as EARLIEST_DATE,
        MAX(db.DCREATEDATE) as LATEST_DATE
    FROM D1_FAMILYINFO f
    INNER JOIN DB_BZCLB db ON f.GID = db.GOBJECTID
    WHERE f.IDELETEMARK = 0
      AND db.IDELETEMARK = 0';
    DBMS_OUTPUT.PUT_LINE('物化视图 MV_UNMIGRATED_FILES_SUMMARY 创建成功');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -955 THEN
            DBMS_OUTPUT.PUT_LINE('物化视图 MV_UNMIGRATED_FILES_SUMMARY 已存在');
        ELSE
            DBMS_OUTPUT.PUT_LINE('创建物化视图失败: ' || SQLERRM);
        END IF;
END;
/

-- 4. 优化查询性能的HINT建议
-- 创建一个性能测试查询
SELECT /*+ FIRST_ROWS(100) USE_INDEX(f IDX_D1_FAMILYINFO_DELETE) USE_INDEX(db IDX_DB_BZCLB_COMPOSITE) */
       COUNT(*) as "待迁移文件数量（前100条估算）"
FROM (
    SELECT /*+ FIRST_ROWS(100) */ 1
    FROM D1_FAMILYINFO f
    INNER JOIN DB_BZCLB db ON f.GID = db.GOBJECTID
    WHERE f.IDELETEMARK = 0
      AND db.IDELETEMARK = 0
      AND db.SPATH IS NOT NULL
      AND ROWNUM <= 100
);

-- 5. 检查索引使用情况
SELECT 
    i.index_name,
    i.table_name,
    i.status,
    i.num_rows,
    i.last_analyzed,
    CASE 
        WHEN i.last_analyzed < SYSDATE - 7 THEN '需要更新统计信息'
        ELSE '统计信息正常'
    END as 统计信息状态
FROM user_indexes i
WHERE i.table_name IN ('DB_BZCLB', 'D1_FAMILYINFO', 'ATTACHMENT_MIGRATION_RECORD')
ORDER BY i.table_name, i.index_name;

-- 6. 性能监控查询
-- 检查当前迁移进度
SELECT 
    '总文件数' as 指标,
    COUNT(*) as 数值
FROM DB_BZCLB 
WHERE SPATH IS NOT NULL AND IDELETEMARK = 0

UNION ALL

SELECT 
    '已迁移文件数',
    COUNT(DISTINCT amr.GOBJECTID)
FROM ATTACHMENT_MIGRATION_RECORD amr
WHERE amr.STATUS = 1

UNION ALL

SELECT 
    '失败文件数',
    COUNT(DISTINCT amr.GOBJECTID)
FROM ATTACHMENT_MIGRATION_RECORD amr
WHERE amr.STATUS = 0
  AND NOT EXISTS (
      SELECT 1 FROM ATTACHMENT_MIGRATION_RECORD amr2
      WHERE amr2.GOBJECTID = amr.GOBJECTID AND amr2.STATUS = 1
  )

UNION ALL

SELECT 
    '待迁移文件数',
    COUNT(*)
FROM DB_BZCLB db
WHERE db.SPATH IS NOT NULL
  AND db.IDELETEMARK = 0
  AND NOT EXISTS (
      SELECT 1 FROM ATTACHMENT_MIGRATION_RECORD amr
      WHERE amr.GOBJECTID = db.GID AND amr.STATUS = 1
  );

-- 7. 清理无效数据（可选）
-- 删除重复的失败记录，只保留最新的
DELETE FROM ATTACHMENT_MIGRATION_RECORD amr1
WHERE amr1.STATUS = 0
  AND EXISTS (
      SELECT 1 FROM ATTACHMENT_MIGRATION_RECORD amr2
      WHERE amr2.GOBJECTID = amr1.GOBJECTID
        AND amr2.STATUS = 0
        AND amr2.CREATED_TIME > amr1.CREATED_TIME
  );

-- 8. 创建性能监控表（可选）
BEGIN
    EXECUTE IMMEDIATE '
    CREATE TABLE MIGRATION_PERFORMANCE_LOG (
        ID VARCHAR2(32) PRIMARY KEY,
        BATCH_NUMBER NUMBER(10),
        BATCH_SIZE NUMBER(10),
        PROCESSING_TIME_MS NUMBER(15),
        SUCCESS_COUNT NUMBER(10),
        FAILED_COUNT NUMBER(10),
        PROCESSING_RATE NUMBER(10,2),
        MEMORY_USAGE_PERCENT NUMBER(5,2),
        CREATED_TIME DATE DEFAULT SYSDATE
    )';
    DBMS_OUTPUT.PUT_LINE('性能监控表 MIGRATION_PERFORMANCE_LOG 创建成功');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -955 THEN
            DBMS_OUTPUT.PUT_LINE('性能监控表 MIGRATION_PERFORMANCE_LOG 已存在');
        ELSE
            DBMS_OUTPUT.PUT_LINE('创建性能监控表失败: ' || SQLERRM);
        END IF;
END;
/

-- 9. 设置数据库参数优化（需要DBA权限）
-- ALTER SYSTEM SET db_file_multiblock_read_count=128 SCOPE=BOTH;
-- ALTER SYSTEM SET hash_join_enabled=TRUE SCOPE=BOTH;
-- ALTER SYSTEM SET optimizer_index_cost_adj=20 SCOPE=BOTH;

COMMIT;

-- 输出优化完成信息
SELECT 
    '性能优化完成' as 状态,
    SYSDATE as 完成时间,
    '建议重启应用以获得最佳性能' as 建议
FROM DUAL;
