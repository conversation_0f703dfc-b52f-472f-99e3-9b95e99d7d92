# 高性能迁移配置 - 提升迁移效率
# 请将以下配置合并到 application-prod.yml 中

# 数据库连接池优化 - 提高并发能力
spring:
  datasource:
    dynamic:
      datasource:
        master:
          hikari:
            maximum-pool-size: 30        # 增加最大连接数
            minimum-idle: 15             # 增加最小空闲连接
            connection-timeout: 20000    # 减少连接超时
            idle-timeout: 300000         # 减少空闲超时
            max-lifetime: 1200000        # 减少最大生命周期
            leak-detection-threshold: 30000 # 减少泄漏检测时间
        slave_1:
          hikari:
            maximum-pool-size: 30
            minimum-idle: 15
            connection-timeout: 20000
            idle-timeout: 300000
            max-lifetime: 1200000
            leak-detection-threshold: 30000

# 迁移任务高性能配置
migration:
  # 基础配置 - 提高处理能力
  batch:
    size: 200                           # 增加批次大小
  max:
    concurrent:
      tasks: 15                         # 增加并发任务数
    retry:
      count: 1                          # 保持重试次数为1
  cron:
    expression: 0 0/3 * * * ?           # 每3分钟执行一次
  switch:
    family: true                        # 家庭表任务开启
    people: false                       # 成员表任务关闭

  bulk:
    # 基础配置 - 提高并发处理能力
    chunk-size: 5000                    # 增加每次查询条数
    parallel-threads: 20                # 增加并行线程数
    batch-insert-size: 1000             # 增加批量插入大小

    # 超时配置 - 优化网络性能
    connection-timeout: 15000           # 减少连接超时
    read-timeout: 60000                 # 减少读取超时到1分钟
    retry-interval: 500                 # 减少重试间隔

    # 重试配置
    max-retry-count: 1                  # 减少最大重试次数
    enable-retry: true                  # 启用失败重试

    # 功能开关配置
    enable-resume: true                 # 启用断点续传
    enable-monitoring: true             # 启用性能监控

    # 性能优化配置
    gc-trigger-threshold: 100000        # 10万条触发GC
    progress-report-interval: 2000      # 每2000条报告进度

    # 持续监控配置
    enable-continuous-monitoring: true   # 启用持续监控
    continuous-monitoring-interval: 5000 # 5秒检查间隔
    skip-failed-records: true          # 跳过失败记录

    # 高性能模式配置
    db-pool-size: 40                    # 增加数据库连接池
    oss-upload-concurrency: 100         # 增加OSS上传并发
    memory-queue-size: 10000            # 增加内存队列大小
    prefetch-batches: 3                 # 增加预取批次
    enable-batch-prefetch: true         # 启用批量预取

    # 性能监控配置
    performance-monitor-interval: 15     # 15秒监控间隔
    target-processing-speed: 500        # 目标500条/秒

    # 动态线程调整
    enable-dynamic-thread-adjustment: true
    max-threads: 40                     # 增加最大线程数
    min-threads: 10                     # 增加最小线程数

    # 文件处理配置
    max-file-size: 314572800           # 最大文件300MB
    download-buffer-size: 131072        # 下载缓冲128KB
    delete-temp-files: true             # 删除临时文件
    supported-file-types: "jpg,jpeg,png,gif,bmp,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar"
    temp-directory: D:\temp

    # 数据库配置
    db-max-wait-time: 3000              # 减少数据库最大等待时间
    batch-queue-capacity: 5000          # 增加批处理队列

# 线程池配置 - 提高并发处理能力
thread:
  pool:
    file-migration:
      core-pool-size: 20                # 增加核心线程数
      maximum-pool-size: 40            # 增加最大线程数
      keep-alive-time: 30              # 减少空闲时间
      queue-capacity: 1000             # 增加队列容量
      thread-name-prefix: "FileMigration-"

# JVM优化建议（启动参数）
# -Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication
